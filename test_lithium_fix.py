#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from electrochemistry.material_collection import MaterialCollection
    from electrochemistry.active_material_blend import ActiveMaterialBlend
    from electrochemistry.active_material import ActiveMaterialWeight
    
    print("Testing lithium anode with anodeQAim = 10000...")
    
    # Load material collection
    import os
    materials_path = 'data/materials'
    print(f"Materials path exists: {os.path.exists(materials_path)}")
    print(f"Materials path contents: {os.listdir(materials_path) if os.path.exists(materials_path) else 'N/A'}")

    # Check if lithium anode file exists
    lithium_file = 'data/materials/anode/Lithium_Anode.json'
    print(f"Lithium anode file exists: {os.path.exists(lithium_file)}")

    material_collection = MaterialCollection(materials_path)

    # Debug: Check if lithium_anode definition exists
    print("Available material definitions:", list(material_collection.material_definitions.keys()))

    # Try to get the material definition first
    definition = material_collection.material_definitions.get('lithium_anode')
    if definition is None:
        print("❌ lithium_anode definition not found!")
        exit(1)
    else:
        print("✅ lithium_anode definition found:", definition.name)

    lithium_anode = material_collection.get_active_material('lithium_anode')
    
    print(f"Loaded lithium anode: {lithium_anode.name}")
    
    # Test the original capacity
    c10_discharge = lithium_anode.formations.get_formation_c_10_discharge()
    if c10_discharge:
        print(f"Original C/10 discharge capacity: {c10_discharge.capacity}")
        print(f"Original q_min: {c10_discharge.q_min}, q_max: {c10_discharge.q_max}")
        print(f"Original u_nom: {c10_discharge.u_nom}")
        print(f"Number of data points: {len(c10_discharge.cycle) if c10_discharge.cycle is not None else 0}")
    
    # Test with anodeQAim = 10000 (the problematic case)
    print("\nTesting with anodeQAim = 10000...")
    try:
        blend = ActiveMaterialBlend(
            active_materials=[ActiveMaterialWeight(lithium_anode, 1.0)],
            formations=None,
            q_aim=10000.0
        )
        
        c10_discharge_scaled = blend.formations.get_formation_c_10_discharge()
        if c10_discharge_scaled:
            print(f"Scaled C/10 discharge capacity: {c10_discharge_scaled.capacity}")
            print(f"Scaled q_min: {c10_discharge_scaled.q_min}, q_max: {c10_discharge_scaled.q_max}")
            print(f"Scaled u_nom: {c10_discharge_scaled.u_nom}")
            
            # Check for NaN values in the cycle data
            if c10_discharge_scaled.cycle is not None:
                nan_voltage_count = c10_discharge_scaled.cycle['SpannungV'].isna().sum()
                nan_capacity_count = c10_discharge_scaled.cycle['LadungMAhg'].isna().sum()
                print(f"NaN voltage values: {nan_voltage_count}")
                print(f"NaN capacity values: {nan_capacity_count}")
                
                if nan_voltage_count == 0:
                    print("✅ SUCCESS: No NaN voltage values found!")
                else:
                    print("❌ ISSUE: Still have NaN voltage values")
            else:
                print("❌ ISSUE: Cycle data is None")
        else:
            print("❌ ISSUE: C/10 discharge formation is None")
            
    except Exception as e:
        print(f"❌ ERROR during scaling: {e}")
        import traceback
        traceback.print_exc()
    
    # Test with anodeQAim = 9000 (the working case)
    print("\nTesting with anodeQAim = 9000...")
    try:
        blend = ActiveMaterialBlend(
            active_materials=[ActiveMaterialWeight(lithium_anode, 1.0)],
            formations=None,
            q_aim=9000.0
        )
        
        c10_discharge_scaled = blend.formations.get_formation_c_10_discharge()
        if c10_discharge_scaled:
            print(f"Scaled C/10 discharge capacity: {c10_discharge_scaled.capacity}")
            print(f"Scaled u_nom: {c10_discharge_scaled.u_nom}")
            
            # Check for NaN values in the cycle data
            if c10_discharge_scaled.cycle is not None:
                nan_voltage_count = c10_discharge_scaled.cycle['SpannungV'].isna().sum()
                print(f"NaN voltage values: {nan_voltage_count}")
                
                if nan_voltage_count == 0:
                    print("✅ SUCCESS: No NaN voltage values found!")
                else:
                    print("❌ ISSUE: Still have NaN voltage values")
            else:
                print("❌ ISSUE: Cycle data is None")
        else:
            print("❌ ISSUE: C/10 discharge formation is None")
            
    except Exception as e:
        print(f"❌ ERROR during scaling: {e}")
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f"❌ IMPORT ERROR: {e}")
    import traceback
    traceback.print_exc()
